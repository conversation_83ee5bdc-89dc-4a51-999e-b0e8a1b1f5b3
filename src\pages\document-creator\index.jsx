import React, { useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import Header from '../../components/ui/Header';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import StatusNotification from '../../components/ui/StatusNotification';
import CreationWizard from './components/CreationWizard';

const DocumentCreator = () => {
  const { contentMargin } = useSidebar();
  const location = useLocation();
  const preselectedBaseline = location.state?.preselectedBaseline;

  const [documentData, setDocumentData] = useState({
    documentType: 'ebook',
    language: 'english',
    tone: 'academic',
    title: 'Untitled Document',
    format: 'pdf',
    baseline: preselectedBaseline || 'template'
  });
  const [currentStep, setCurrentStep] = useState(1);



  const handleDocumentDataChange = useCallback((newData) => {
    setDocumentData(prev => ({ ...prev, ...newData }));
  }, []);

  const handleStepChange = useCallback((step) => {
    setCurrentStep(step);
  }, []);



  return (
    <div className="min-h-screen bg-background">
      {/* Header and StatusNotification removed for clean, full-screen Designrr-style experience */}
      <QuickActionSidebar />

      {/* Main Content - Designrr Style Clean Layout */}
      <main className={`${contentMargin} transition-all duration-300 ease-in-out`}>
        {/* Full Screen Wizard Mode - No Headers or Toolbars for Clean Look */}
        <div className="h-screen">
          <CreationWizard
            currentStep={currentStep}
            onStepChange={handleStepChange}
            documentData={documentData}
            onDocumentDataChange={handleDocumentDataChange}
          />
        </div>
      </main>
    </div>
  );
};

export default DocumentCreator;