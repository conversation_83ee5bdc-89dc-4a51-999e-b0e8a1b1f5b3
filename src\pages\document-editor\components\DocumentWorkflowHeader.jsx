import React from 'react';
import Icon from '../../../components/AppIcon';
import MobileMenuButton from '../../../components/ui/MobileMenuButton';
import { useSidebar } from '../../../contexts/SidebarContext';
import { STANDARD_WORKFLOW_PHASES, getPhaseState } from '../../../utils/phaseMapping';

/**
 * DocumentWorkflowHeader - Reusable header for document workflows
 * Shows 4-phase progress indicator: Generate → Edit Content → Review → Publish
 * Supports both document editor and document creator workflows
 */
const DocumentWorkflowHeader = ({
  currentPhase = 'Edit Content',
  onPhaseClick = null,
  className = '',
  // New props for flexibility
  phases = null, // Custom phases array, defaults to STANDARD_WORKFLOW_PHASES
  mode = 'editor', // 'editor' | 'creator'
  showStepCounter = false, // Show detailed step counter for creator mode
  currentStep = 1, // Current step number (for creator mode)
  totalSteps = 8, // Total steps (for creator mode)
  headerTitle = null // Custom title for creator mode
}) => {
  const { isMobileSidebarOpen } = useSidebar();

  // Use custom phases or default to standard workflow phases
  const workflowPhases = phases || STANDARD_WORKFLOW_PHASES;

  // Determine phase states based on current phase
  const getPhaseStateForPhase = (phase) => {
    return getPhaseState(phase.id, currentPhase);
  };

  // Handle phase click
  const handlePhaseClick = (phase) => {
    const state = getPhaseStateForPhase(phase);
    if ((state === 'completed' || state === 'active') && onPhaseClick) {
      onPhaseClick(phase.id);
    }
  };

  // Get current phase index for progress calculation
  const currentIndex = workflowPhases.findIndex(p => p.id === currentPhase);

  return (
    <div
      className={`fixed top-0 left-0 right-0 w-full border-b border-border shadow-sm z-1010 ${className}`}
      style={{ backgroundColor: 'red', minHeight: '64px' }}
      data-testid="workflow-header-debug"
    >

      {/* Mobile Layout: < 640px - Compact horizontal bar */}
      <div className="block sm:hidden px-4 py-3">
        <div className="flex items-center justify-between">
          <MobileMenuButton isOpen={isMobileSidebarOpen} />
          <div className="flex-1 text-center mx-4">
            {mode === 'creator' && headerTitle ? (
              <>
                <div className="text-sm font-medium text-primary">{headerTitle}</div>
                {showStepCounter && (
                  <div className="text-xs text-text-secondary">
                    Step {currentStep} of {totalSteps}
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="text-sm font-medium text-primary">{currentPhase}</div>
                <div className="text-xs text-text-secondary">
                  Step {currentIndex + 1} of {workflowPhases.length}
                </div>
              </>
            )}
          </div>
          <div className="flex space-x-1">
            {workflowPhases.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  index <= currentIndex ? 'bg-primary' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Tablet Layout: 640px - 1024px - Icon-only compact */}
      <div className="hidden sm:block lg:hidden py-4 px-4">
        <div className="relative flex items-center justify-center">
          <div className="absolute left-0">
            <MobileMenuButton isOpen={isMobileSidebarOpen} />
          </div>
          <div className="flex items-center space-x-3">
            {workflowPhases.map((phase, index) => {
              const state = getPhaseStateForPhase(phase);
              const isActive = state === 'active';
              const isCompleted = state === 'completed';
              const isClickable = state === 'completed' || state === 'active';

              return (
                <React.Fragment key={phase.id}>
                  {/* Tablet Phase Circle - Icon only */}
                  <button
                    onClick={() => handlePhaseClick(phase)}
                    disabled={!isClickable}
                    className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold
                      transition-all duration-300 relative z-10
                      ${isActive
                        ? 'bg-primary text-white shadow-md ring-2 ring-primary/20'
                        : isCompleted
                          ? 'bg-primary text-white hover:bg-primary/90'
                          : 'bg-gray-200 text-gray-500'
                      }
                      ${isClickable ? 'cursor-pointer hover:shadow-lg hover:scale-105' : 'cursor-not-allowed'}
                    `}
                    title={`${phase.title} - ${phase.description}`}
                  >
                    {isCompleted ? (
                      <Icon name="Check" size={14} />
                    ) : (
                      <Icon name={phase.icon} size={14} />
                    )}
                  </button>

                  {/* Connection Line */}
                  {index < workflowPhases.length - 1 && (
                    <div
                      className={`
                        h-0.5 w-6 transition-all duration-500
                        ${isCompleted ? 'bg-primary' : 'bg-gray-200'}
                      `}
                    />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>
      </div>

      {/* Desktop Layout: ≥ 1024px - Full layout */}
      <div className="hidden lg:block">
        <div className="flex items-center justify-center space-x-4 md:space-x-8 py-3 px-4">
          {workflowPhases.map((phase, index) => {
            const state = getPhaseStateForPhase(phase);
            const isActive = state === 'active';
            const isCompleted = state === 'completed';
            const isClickable = state === 'completed' || state === 'active';

            return (
              <React.Fragment key={phase.id}>
                {/* Phase Item */}
                <div className="flex items-center space-x-2 md:space-x-3">
                  {/* Phase Circle */}
                  <button
                    onClick={() => handlePhaseClick(phase)}
                    disabled={!isClickable}
                    className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold
                      transition-all duration-300 relative z-10
                      ${isActive
                        ? 'bg-primary text-white shadow-md ring-2 ring-primary/20'
                        : isCompleted
                          ? 'bg-primary text-white hover:bg-primary/90'
                          : 'bg-gray-200 text-gray-500'
                      }
                      ${isClickable ? 'cursor-pointer hover:shadow-lg hover:scale-105' : 'cursor-not-allowed'}
                    `}
                    title={phase.description}
                  >
                    {isCompleted ? (
                      <Icon name="Check" size={14} />
                    ) : (
                      <Icon name={phase.icon} size={14} />
                    )}
                  </button>

                  {/* Phase Label */}
                  <div className="text-left">
                    <div className={`text-sm font-medium transition-colors duration-200 ${
                      isActive
                        ? 'text-primary'
                        : isCompleted
                          ? 'text-text-primary'
                          : 'text-text-secondary'
                    }`}>
                      {phase.title}
                    </div>
                    {/* Optional description - shown only for active phase */}
                    {isActive && (
                      <div className="text-xs text-text-secondary mt-0.5">
                        {phase.description}
                      </div>
                    )}
                  </div>
                </div>

                {/* Connection Line */}
                {index < workflowPhases.length - 1 && (
                  <div
                    className={`
                      h-0.5 w-8 md:w-12 transition-all duration-500
                      ${isCompleted ? 'bg-primary' : 'bg-gray-200'}
                    `}
                  />
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DocumentWorkflowHeader;
